{"Version": 1, "WorkspaceRootPath": "E:\\Projects\\abozyad\\OrderFlowCore\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\wwwroot\\js\\hrcoordinator.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\wwwroot\\js\\hrcoordinator.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\wwwroot\\js\\supervisororders.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\wwwroot\\js\\supervisororders.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|ViewDataWindow_MSSQL__/._SQLEXPRESS01/OrderFlowCore2/True/SqlTable/dbo.ordersTable.sql||{0058A1F7-65F3-4DB9-B3D0-CA7E64DD73CD}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\supervisororders\\processorder.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\supervisororders\\processorder.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\supervisororders\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\supervisororders\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{750D6A94-0B0A-43DE-900B-6EF5B2F93B95}|OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.infrastructure\\repositories\\orderrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{750D6A94-0B0A-43DE-900B-6EF5B2F93B95}|OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj|solutionrelative:orderflowcore.infrastructure\\repositories\\orderrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\supervisororderservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\supervisororderservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\supervisororderscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\supervisororderscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\supervisorservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\supervisorservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\roleservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\roleservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\assistantmanagercontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\assistantmanagercontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\accountscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\accountscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\dashboardservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\dashboardservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\ordermanager\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\ordermanager\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DDC2C32B-0409-4942-94F5-65603C464384}|OrderFlowCore.Core\\OrderFlowCore.Core.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.core\\entities\\supervisortype.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{DDC2C32B-0409-4942-94F5-65603C464384}|OrderFlowCore.Core\\OrderFlowCore.Core.csproj|solutionrelative:orderflowcore.core\\entities\\supervisortype.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\shared\\_orderdetailspartial.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\shared\\_orderdetailspartial.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|ViewDataWindow_MSSQL__/._SQLEXPRESS01/OrderFlowCore2/True/SqlTable/dbo.Users.sql||{0058A1F7-65F3-4DB9-B3D0-CA7E64DD73CD}"}, {"AbsoluteMoniker": "D:0:0:{DDC2C32B-0409-4942-94F5-65603C464384}|OrderFlowCore.Core\\OrderFlowCore.Core.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.core\\entities\\orderstatus.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{DDC2C32B-0409-4942-94F5-65603C464384}|OrderFlowCore.Core\\OrderFlowCore.Core.csproj|solutionrelative:orderflowcore.core\\entities\\orderstatus.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{DDC2C32B-0409-4942-94F5-65603C464384}|OrderFlowCore.Core\\OrderFlowCore.Core.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.core\\entities\\orderstatusextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{DDC2C32B-0409-4942-94F5-65603C464384}|OrderFlowCore.Core\\OrderFlowCore.Core.csproj|solutionrelative:orderflowcore.core\\entities\\orderstatusextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\hrcoordinatororderservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\hrcoordinatororderservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\accounts\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\accounts\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\extentions\\claimsprincipalextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\extentions\\claimsprincipalextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\dtos\\accountsdto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\dtos\\accountsdto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "hrCoordinator.js", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\hrCoordinator.js", "RelativeDocumentMoniker": "OrderFlowCore.Web\\wwwroot\\js\\hrCoordinator.js", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\hrCoordinator.js", "RelativeToolTip": "OrderFlowCore.Web\\wwwroot\\js\\hrCoordinator.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-07-18T13:35:38.933Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "dbo.ordersTable [Data]", "DocumentMoniker": "ViewDataWindow_MSSQL__/._SQLEXPRESS01/OrderFlowCore2/True/SqlTable/dbo.ordersTable.sql", "ToolTip": "dbo.ordersTable [Data]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-18T13:23:16.855Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "supervisorOrders.js", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\supervisorOrders.js", "RelativeDocumentMoniker": "OrderFlowCore.Web\\wwwroot\\js\\supervisorOrders.js", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\supervisorOrders.js", "RelativeToolTip": "OrderFlowCore.Web\\wwwroot\\js\\supervisorOrders.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAABgAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-07-18T13:04:23.962Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ProcessOrder.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\SupervisorOrders\\ProcessOrder.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\SupervisorOrders\\ProcessOrder.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\SupervisorOrders\\ProcessOrder.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\SupervisorOrders\\ProcessOrder.cshtml", "ViewState": "AgIAAJAAAAAAAAAAAAAAAJoAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-18T12:52:18.321Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Index.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\SupervisorOrders\\Index.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\SupervisorOrders\\Index.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\SupervisorOrders\\Index.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\SupervisorOrders\\Index.cshtml", "ViewState": "AgIAADMAAAAAAAAAAAAAAEkAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-18T12:51:52.654Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "SupervisorOrderService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\SupervisorOrderService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\SupervisorOrderService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\SupervisorOrderService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\SupervisorOrderService.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAnwCEAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T12:47:48.84Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "HRCoordinatorOrderService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\HRCoordinatorOrderService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\HRCoordinatorOrderService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\HRCoordinatorOrderService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\HRCoordinatorOrderService.cs", "ViewState": "AgIAAH8AAAAAAAAAAAAEwJcAAACCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T12:46:34.071Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "OrderStatusExtensions.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\Entities\\OrderStatusExtensions.cs", "RelativeDocumentMoniker": "OrderFlowCore.Core\\Entities\\OrderStatusExtensions.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\Entities\\OrderStatusExtensions.cs", "RelativeToolTip": "OrderFlowCore.Core\\Entities\\OrderStatusExtensions.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T12:45:58.739Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "OrderStatus.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\Entities\\OrderStatus.cs", "RelativeDocumentMoniker": "OrderFlowCore.Core\\Entities\\OrderStatus.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\Entities\\OrderStatus.cs", "RelativeToolTip": "OrderFlowCore.Core\\Entities\\OrderStatus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T12:45:54.571Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "dbo.Users [Data]", "DocumentMoniker": "ViewDataWindow_MSSQL__/._SQLEXPRESS01/OrderFlowCore2/True/SqlTable/dbo.Users.sql", "ToolTip": "dbo.Users [Data]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-18T12:39:01.712Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "_OrderDetailsPartial.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\Shared\\_OrderDetailsPartial.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\Shared\\_OrderDetailsPartial.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\Shared\\_OrderDetailsPartial.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\Shared\\_OrderDetailsPartial.cshtml", "ViewState": "AgIAAFcAAAAAAAAAAAAAAFcAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-18T12:37:45.736Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "SupervisorType.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\Entities\\SupervisorType.cs", "RelativeDocumentMoniker": "OrderFlowCore.Core\\Entities\\SupervisorType.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\Entities\\SupervisorType.cs", "RelativeToolTip": "OrderFlowCore.Core\\Entities\\SupervisorType.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAawB0AAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T12:37:14.48Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "Index.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\OrderManager\\Index.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\OrderManager\\Index.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\OrderManager\\Index.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\OrderManager\\Index.cshtml", "ViewState": "AgIAAEgAAAAAAAAAAAAAAEwAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-18T12:37:06.984Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "DashboardService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\DashboardService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\DashboardService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\DashboardService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\DashboardService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADIBAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T12:36:28.115Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "AccountsController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AccountsController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\AccountsController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AccountsController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\AccountsController.cs", "ViewState": "AgIAAFUAAAAAAAAAAAAcwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T12:35:24.906Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "AssistantManagerController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T12:35:18.071Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "RoleService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\RoleService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\RoleService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\RoleService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\RoleService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T12:34:19.44Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "OrderRepository.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\Repositories\\OrderRepository.cs", "RelativeDocumentMoniker": "OrderFlowCore.Infrastructure\\Repositories\\OrderRepository.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\Repositories\\OrderRepository.cs", "RelativeToolTip": "OrderFlowCore.Infrastructure\\Repositories\\OrderRepository.cs", "ViewState": "AgIAAGkAAAAAAAAAAAAawIAAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T12:31:56.048Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "SupervisorService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\SupervisorService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\SupervisorService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\SupervisorService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\SupervisorService.cs", "ViewState": "AgIAAEoAAAAAAAAAAADwv30AAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T12:30:04.157Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "ClaimsPrincipalExtensions.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Extentions\\ClaimsPrincipalExtensions.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Extentions\\ClaimsPrincipalExtensions.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Extentions\\ClaimsPrincipalExtensions.cs", "RelativeToolTip": "OrderFlowCore.Web\\Extentions\\ClaimsPrincipalExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T08:18:30.599Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "AuthController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\AuthController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AuthController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\AuthController.cs", "ViewState": "AgIAAHYAAAAAAAAAAAA4wKIAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T08:07:06.599Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "SupervisorOrdersController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "ViewState": "AgIAABQAAAAAAAAAAAA1wCQAAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T07:59:13.309Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "AccountsDto.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\DTOs\\AccountsDto.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\DTOs\\AccountsDto.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\DTOs\\AccountsDto.cs", "RelativeToolTip": "OrderFlowCore.Application\\DTOs\\AccountsDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T07:51:50.177Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "Index.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\Accounts\\Index.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\Accounts\\Index.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\Accounts\\Index.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\Accounts\\Index.cshtml", "ViewState": "AgIAAIUBAAAAAAAAAAAtwJgBAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-18T07:51:42.411Z", "EditorCaption": ""}]}]}]}