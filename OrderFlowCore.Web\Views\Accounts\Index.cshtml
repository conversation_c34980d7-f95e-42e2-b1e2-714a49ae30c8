@using OrderFlowCore.Core.Entities
@model OrderFlowCore.Application.DTOs.AccountsDto
@{
    ViewData["Title"] = "إدارة الحسابات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">إدارة الحسابات</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Dashboard")">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active" aria-current="page">إدارة الحسابات</li>
                        </ol>
                    </nav>
                </div>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="fas fa-plus me-2"></i>إضافة مستخدم جديد
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">إدارة المستخدمين</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form asp-action="SearchUsers" method="post" class="d-flex">
                                <input type="text" name="searchTerm" class="form-control me-2" placeholder="البحث عن مستخدم..." value="@Model.UserSearchTerm">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                    @if (Model.Users.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم المستخدم</th>
                                        <th>القسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var user in Model.Users)
                                    {
                                        <tr>
                                            <td><strong>@user.Username</strong></td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(user.RoleType))
                                                {
                                                    <span class="badge bg-primary">@user.UserRole.ToDisplayName()</span>
                                                    <br />
                                                    <small class="text-muted">@user.RoleType</small>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">@user.UserRole.ToDisplayName()</span>
                                                }
                                            </td>
                                            <td>@(user.Email ?? "-")</td>
                                            <td>@(user.Phone ?? "-")</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="editUser('@user.Username', '@user.Email', '@user.Phone')" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="confirmDeleteUser('@user.Username')" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-warning" onclick="openChangePasswordModal('@user.Username')" title="تغيير كلمة المرور">
                                                        <i class="bi bi-key"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد حسابات</h5>
                            <p class="text-muted">قم بإضافة مستخدم جديد للبدء</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                <i class="fas fa-plus me-2"></i>إضافة مستخدم جديد
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Improved Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content shadow">
            <!-- Header with gradient background -->
            <div class="modal-header bg-primary text-white border-0">
                <h5 class="modal-title d-flex align-items-center" id="addUserModalLabel">
                    <i class="bi bi-person-plus me-2"></i>
                    إضافة مستخدم جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>

            <form asp-action="CreateUser" method="post" novalidate>
                <div class="modal-body bg-light">
                    <!-- Anti-forgery token -->
                    <!-- @Html.AntiForgeryToken() -->
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger d-none"></div>

                    <!-- Basic Information Section -->
                    <div class="card mb-4 border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <h6 class="card-title mb-0 d-flex align-items-center text-primary">
                                <i class="bi bi-person-badge me-2"></i>
                                المعلومات الأساسية
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="username" class="form-label fw-semibold">
                                        <i class="bi bi-person me-1"></i>
                                        اسم المستخدم
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control form-control-lg"
                                           id="username"
                                           name="Username"
                                           autocomplete="off"
                                           placeholder="أدخل اسم المستخدم"
                                           required />
                                    <span asp-validation-for="AddModel.Username" class="text-danger small"></span>
                                </div>

                                <div class="col-md-6">
                                    <label for="password" class="form-label fw-semibold">
                                        <i class="bi bi-lock me-1"></i>
                                        كلمة المرور
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="password"
                                               class="form-control form-control-lg"
                                               id="password"
                                               name="Password"
                                               minlength="6"
                                               autocomplete="new-password"
                                               placeholder="أدخل كلمة المرور"
                                               required />
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                            <i class="bi bi-eye" id="toggleIcon"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        يجب أن تحتوي كلمة المرور على 6 أحرف على الأقل
                                    </div>
                                    <span asp-validation-for="AddModel.Password" class="text-danger small"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information Section -->
                    <div class="card mb-4 border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <h6 class="card-title mb-0 d-flex align-items-center text-info">
                                <i class="bi bi-envelope me-2"></i>
                                معلومات التواصل
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="email" class="form-label fw-semibold">
                                        <i class="bi bi-envelope me-1"></i>
                                        البريد الإلكتروني
                                    </label>
                                    <input type="email"
                                           class="form-control form-control-lg"
                                           id="email"
                                           name="Email"
                                           placeholder="<EMAIL>" required />
                                    <span asp-validation-for="AddModel.Email" class="text-danger small"></span>
                                </div>

                                <div class="col-md-6">
                                    <label for="phone" class="form-label fw-semibold">
                                        <i class="bi bi-telephone me-1"></i>
                                        رقم الهاتف
                                    </label>
                                    <input type="tel"
                                           class="form-control form-control-lg"
                                           id="phone"
                                           name="Phone"
                                           placeholder="+96 1234567890" required />
                                    <span asp-validation-for="AddModel.Phone" class="text-danger small"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Role & Permissions Section -->
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <h6 class="card-title mb-0 d-flex align-items-center text-success">
                                <i class="bi bi-shield-check me-2"></i>
                                الصلاحيات والأدوار
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="userRole" class="form-label fw-semibold">
                                        <i class="bi bi-person-gear me-1"></i>
                                        نوع الدور
                                        <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select form-select-lg" id="userRole" name="UserRole" onchange="updateRoleTypes()" required>
                                        <option value="">اختر نوع الدور</option>
                                        <option value="1">مدير مباشر</option>
                                        <option value="2">مساعد مدير</option>
                                        <option value="3">منسق الموارد البشرية</option>
                                        <option value="4">مشرف</option>
                                        <option value="5">المدير</option>
                                    </select>
                                    <span asp-validation-for="AddModel.UserRole" class="text-danger small"></span>
                                </div>

                                <div class="col-md-6" style="display: none;">
                                    <label for="roleType" class="form-label fw-semibold">
                                        <i class="bi bi-briefcase me-1"></i>
                                        تخصص الدور
                                    </label>
                                    <select class="form-select form-select-lg" id="roleType" name="RoleType">
                                        <option value="">اختر التخصص</option>
                                    </select>
                                    <span asp-validation-for="AddModel.RoleType" class="text-danger small"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer bg-white border-top-0 pt-3">
                    <div class="d-flex gap-2 w-100 justify-content-end">
                        <button type="button" class="btn btn-outline-secondary btn-lg px-4" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-2"></i>
                            إلغاء
                        </button>
                        <button type="submit" class="btn btn-primary btn-lg px-4">
                            <i class="bi bi-plus-circle me-2"></i>
                            إضافة المستخدم
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>


<!-- Modal for editing user -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">تعديل بيانات المستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form asp-action="UpdateUser" method="post">
                <div class="modal-body">
                    @Html.AntiForgeryToken()
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    <div class="mb-3">
                        <label for="editUsername" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="editUsername" name="Username" value="@Model.EditModel.Username" readonly required />
                        <span asp-validation-for="EditModel.Username" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="editEmail" name="Email" value="@Model.EditModel.Email" required />
                        <span asp-validation-for="EditModel.Email" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <label for="editPhone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="editPhone" name="Phone" value="@Model.EditModel.Phone" required />
                        <span asp-validation-for="EditModel.Phone" class="text-danger"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteUserModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المستخدم: <strong id="deleteUsername"></strong>؟</p>
                <p class="text-danger"><small>لا يمكن التراجع عن هذا الإجراء.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteUserForm" asp-action="DeleteUser" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <input type="hidden" id="deleteUsernameInput" name="username" />
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changePasswordModalLabel">تغيير كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form id="changePasswordForm" asp-action="ChangeUserPassword" method="post">
                @Html.AntiForgeryToken()
                <div class="modal-body">
                    <input type="hidden" id="changePasswordUsername" name="Username" />
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="newPassword" name="NewPassword" minlength="6" required />
                        <span asp-validation-for="ChangePasswordModel.NewPassword" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                        <input type="password" class="form-control" id="confirmPassword" name="ConfirmPassword" minlength="6" required />
                        <span asp-validation-for="ChangePasswordModel.ConfirmPassword" class="text-danger"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-warning">تغيير كلمة المرور</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="~/Views/Shared/_ValidationScriptsPartial.cshtml" />
    <script>
        // Role type data
        const roleTypes = {
            1: [ // DirectManager - departments
                "قسم إدارة الأصول",
                "قسم المشتريات",
                "قسم الصيانة",
                "قسم المخازن",
                "قسم الموارد البشرية",
                "قسم المالية",
                "قسم تقنية المعلومات",
                "قسم الخدمات الطبية",
                "قسم التمريض",
                "قسم الخدمات الإدارية"
            ],
            2: [ // AssistantManager
                "مساعد المدير للخدمات الطبية",
                "مساعد المدير لخدمات التمريض",
                "مساعد المدير للخدمات الإدارية والتشغيل",
                "مساعد المدير للموارد البشرية"
            ],
            3: [], // Coordinator - no role types
            4: [ // Supervisor
                "خدمات الموظفين",
                "إدارة تخطيط الموارد البشرية",
                "إدارة تقنية المعلومات",
                "مراقبة الدوام",
                "السجلات الطبية",
                "إدارة الرواتب والاستحقاقات",
                "إدارة القانونية والالتزام",
                "خدمات الموارد البشرية",
                "إدارة الإسكان",
                "قسم الملفات",
                "العيادات الخارجية",
                "التأمينات الاجتماعية",
                "وحدة مراقبة المخزون",
                "إدارة تنمية الإيرادات",
                "إدارة الأمن و السلامة",
                "الطب الاتصالي"
            ],
            5: [] // Manager - no role types
        };

        function updateRoleTypes() {
            const userRoleSelect = document.getElementById('userRole');
            const roleTypeSelect = document.getElementById('roleType');
            const roleTypeContainer = roleTypeSelect.closest('div');
            const selectedRole = userRoleSelect.value;

            // Clear existing options
            roleTypeSelect.innerHTML = '<option value="">اختر التخصص</option>';

            // Determine if role type should be shown based on your requirements:
            // Admin (0): don't show
            // DirectManager (1): show with departments
            // AssistantManager (2): show with assistant manager types
            // Coordinator (3): don't show
            // Supervisor (4): show with supervisor types
            // Manager (5): don't show

            const shouldShowRoleType = selectedRole === '1' || selectedRole === '2' || selectedRole === '4';

            if (shouldShowRoleType) {
                roleTypeContainer.style.display = 'block';
                roleTypeSelect.setAttribute('required', 'required'); // Add required attribute

                if (selectedRole && roleTypes[selectedRole] && roleTypes[selectedRole].length > 0) {
                    roleTypes[selectedRole].forEach(function(type) {
                        const option = document.createElement('option');
                        option.value = type;
                        option.textContent = type;
                        roleTypeSelect.appendChild(option);
                    });
                }
            } else {
                roleTypeContainer.style.display = 'none';
                roleTypeSelect.value = ''; // Clear the value when hidden
                //remove required
                roleTypeSelect.removeAttribute('required');
            }
        }

        function editUser(username, email, phone) {
            document.getElementById('editUsername').value = username;
            document.getElementById('editEmail').value = email;
            document.getElementById('editPhone').value = phone;
            new bootstrap.Modal(document.getElementById('editUserModal')).show();
        }
        function confirmDeleteUser(username) {
            document.getElementById('deleteUsername').textContent = username;
            document.getElementById('deleteUsernameInput').value = username;
            new bootstrap.Modal(document.getElementById('deleteUserModal')).show();
        }
         function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }

        function openChangePasswordModal(username) {
            document.getElementById('changePasswordUsername').value = username;
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmPassword').value = '';
            new bootstrap.Modal(document.getElementById('changePasswordModal')).show();
        }

        // Bootstrap form validation
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');

            Array.from(forms).forEach(form => {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                });

                // Real-time validation feedback
                const inputs = form.querySelectorAll('input, select');
                inputs.forEach(input => {
                    input.addEventListener('blur', function() {
                        if (this.checkValidity()) {
                            this.classList.remove('is-invalid');
                            this.classList.add('is-valid');
                        } else {
                            this.classList.remove('is-valid');
                            this.classList.add('is-invalid');
                        }
                    });
                });
            });
        });

        // Toastr notifications
        @if (TempData["SuccessMessage"] != null)
        {
                <text>toastr.success('@TempData["SuccessMessage"]');</text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
                <text>toastr.error('@TempData["ErrorMessage"]');</text>
        }
    </script>
}
