@model OrderFlowCore.Web.ViewModels.SupervisorOrdersViewModel
@{
    ViewData["Title"] = "معالجة الطلب";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>معالجة الطلب #@Model.SelectedOrderId</h2>
                <a href="@Url.Action("Index", "SupervisorOrders")" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للقائمة الرئيسية
                </a>
            </div>

            <!-- Alert Panels -->
            <div id="AutoPathInfoPanel" style="display: none;" class="alert alert-info mb-4"></div>
            <div id="RejectionAlertPanel" style="display: none;" class="mb-3"></div>

            @using (Html.BeginForm())
            {
                @Html.AntiForgeryToken()
                @Html.HiddenFor(m => m.SelectedOrderId)


                <!-- Main Processing Section -->
                <div class="row">
                    <!-- Right Column: Actions -->
                    <div class="col-lg-4">
                        <!-- Primary Actions -->
                        <div class="card mb-3 border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-play-circle"></i> الإجراءات الرئيسية</h6>
                            </div>
                            <div class="card-body">
                                <button type="button" class="btn btn-success btn-lg w-100 mb-3" onclick="SupervisorOrders.confirmOrder()">
                                    <i class="fas fa-check-circle"></i> اعتماد الطلب
                                </button>
                                <button type="button" class="btn btn-info btn-lg w-100 mb-3" onclick="SupervisorOrders.markNeedsAction()">
                                    <i class="fas fa-exclamation-triangle"></i> يتطلب إجراءات
                                </button>
                                <button type="button" class="btn btn-danger btn-lg w-100" onclick="SupervisorOrders.rejectOrder()">
                                    <i class="fas fa-times-circle"></i> إعادة الطلب
                                </button>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="card mb-3">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-bolt"></i> إجراءات سريعة</h6>
                            </div>
                            <div class="card-body">
                                <button id="downloadAttachmentsBtn" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-download"></i> تحميل المرفقات
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Left Column: Form Fields -->
                    <div class="col-lg-8">
                        <!-- Form Details Card -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-edit"></i> تفاصيل المعالجة</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="details" class="form-label fw-bold">التفاصيل والرقم</label>
                                    <textarea type="text" class="form-control" id="details" name="details" placeholder="أدخل التفاصيل والرقم هنا..." rows="3"></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="actionRequired" class="form-label fw-bold">الإجراءات المطلوبة</label>
                                    <textarea type="text" class="form-control" id="actionRequired" name="actionRequired" placeholder="حدد الإجراءات المطلوبة..."></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="returnReason" class="form-label fw-bold">سبب الإلغاء/الإعادة</label>
                                    <textarea type="text" class="form-control" id="returnReason" name="returnReason" placeholder="اذكر سبب الإلغاء أو الإعادة..." rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                        <!-- Message Container -->
                        <div id="messageContainer" class="mt-3"></div>
                        <div id="actionRequireMessageContainer" class="mt-3"></div>
                    </div>
                </div>
            }
            

            <!-- Order Details Summary -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-file-alt"></i> ملخص الطلب</h5>
                </div>
                <div class="card-body">
                    @await Html.PartialAsync("_OrderDetailsPartial")
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Order Modal -->
<div class="modal fade" id="confirmOrderModal" tabindex="-1" aria-labelledby="confirmOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmOrderModalLabel">تأكيد الاعتماد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من اعتماد هذا الطلب؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="confirmOrderModalBtn">نعم، اعتماد</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Action Required Modal -->
<div class="modal fade" id="confirmActionRequiredModal" tabindex="-1" aria-labelledby="confirmActionRequiredModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmActionRequiredModalLabel">تأكيد الإجراء المطلوب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من أن هذا الطلب يتطلب إجراءات إضافية؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" id="confirmActionRequiredModalBtn">نعم، يتطلب إجراءات</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Reject Modal -->
<div class="modal fade" id="confirmRejectModal" tabindex="-1" aria-labelledby="confirmRejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmRejectModalLabel">تأكيد إعادة الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من إعادة هذا الطلب؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmRejectModalBtn">نعم، إعادة</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/shared-utils.js"></script>
    <script src="~/js/orderDetailsModule.js"></script>
    <script src="~/js/supervisorOrders.js"></script>
    <script>
        // Show success/error messages from TempData
        @if (TempData["SuccessMessage"] != null)
        {
                <text>OrderDetailsModule.showMessage('@TempData["SuccessMessage"]', 'success');</text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
                <text>OrderDetailsModule.showMessage('@TempData["ErrorMessage"]', 'error');</text>
        }
    </script>
}