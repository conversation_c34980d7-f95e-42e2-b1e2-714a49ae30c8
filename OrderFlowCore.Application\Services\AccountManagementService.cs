using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.Services;

public class AccountManagementService : IAccountManagementService
{
    private readonly IUserService _userService;
    private readonly IRoleService _roleService;
    private readonly IValidationService _validationService;
    private readonly ILogger<AccountManagementService> _logger;

    public AccountManagementService(
        IUserService userService,
        IRoleService roleService,
        IValidationService validationService,
        ILogger<AccountManagementService> logger)
    {
        _userService = userService;
        _roleService = roleService;
        _validationService = validationService;
        _logger = logger;
    }

    public async Task<ServiceResult<AccountsDto>> GetAccountsDataAsync()
    {
        try
        {
            var dto = new AccountsDto();
            await LoadAccountsData(dto);
            // Populate RoleTypes for the view
            dto.RoleTypes[1] = _roleService.GetAvailableRoleTypes(UserRole.DirectManager);
            dto.RoleTypes[2] = _roleService.GetAvailableRoleTypes(UserRole.AssistantManager);
            dto.RoleTypes[3] = _roleService.GetAvailableRoleTypes(UserRole.Coordinator);
            dto.RoleTypes[4] = _roleService.GetAvailableRoleTypes(UserRole.Supervisor);
            dto.RoleTypes[5] = _roleService.GetAvailableRoleTypes(UserRole.Manager);
            return ServiceResult<AccountsDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading accounts data");
            return ServiceResult<AccountsDto>.Failure("حدث خطأ أثناء تحميل بيانات الحسابات");
        }
    }

    public async Task<ServiceResult> CreateUserAsync(UserCreateDto dto)
    {
        try
        {
            // Create user DTO - let UserService handle all validation and existence checks
            var userDto = new UserDto
            {
                Username = dto.Username,
                Email = dto.Email,
                Phone = dto.Phone,
                Password = dto.Password, // Don't hash here - let UserService handle it
                UserRole = dto.UserRole,
                RoleType = dto.RoleType
            };

            var result = await _userService.CreateUserAsync(userDto);
            if (result.IsSuccess)
            {
                return ServiceResult.Success("تم إضافة المستخدم بنجاح");
            }

            return ServiceResult.Failure(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user {Username}", dto.Username);
            return ServiceResult.Failure("حدث خطأ أثناء إضافة المستخدم");
        }
    }

    public async Task<ServiceResult> UpdateUserAsync(UserEditDto dto)
    {
        try
        {
            // Get existing user
            var existingUserResult = await _userService.GetUserByUsernameAsync(dto.Username);
            if (!existingUserResult.IsSuccess || existingUserResult.Data == null)
            {
                return ServiceResult.Failure("المستخدم غير موجود");
            }

            var userDto = existingUserResult.Data;
            userDto.Email = dto.Email ?? string.Empty;
            userDto.Phone = dto.Phone;

            // Let UserService handle all validation
            var result = await _userService.UpdateUserAsync(userDto);
            if (result.IsSuccess)
            {
                return ServiceResult.Success("تم تحديث المستخدم بنجاح");
            }

            return ServiceResult.Failure(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user {Username}", dto.Username);
            return ServiceResult.Failure("حدث خطأ أثناء تحديث المستخدم");
        }
    }

    public async Task<ServiceResult> DeleteUserAsync(string username)
    {
        try
        {
            // Let UserService handle protected user validation
            var result = await _userService.DeleteUserAsync(username);
            if (result.IsSuccess)
            {
                return ServiceResult.Success("تم حذف المستخدم بنجاح");
            }

            return ServiceResult.Failure(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user {Username}", username);
            return ServiceResult.Failure("حدث خطأ أثناء حذف المستخدم");
        }
    }

    public async Task<ServiceResult<AccountsDto>> SearchUsersAsync(string searchTerm)
    {
        try
        {
            var dto = new AccountsDto
            {
                UserSearchTerm = searchTerm
            };

            if (!string.IsNullOrEmpty(searchTerm))
            {
                var usersResult = await _userService.GetAllUsersAsync();
                if (usersResult.IsSuccess)
                {
                    dto.Users = usersResult.Data?
                        .Where(u => u.Username != "Super Admin" &&
                                  u.Username.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                        .ToList() ?? [];
                }
            }
            else
            {
                await LoadAccountsData(dto);
            }
            // Populate RoleTypes for the view
            dto.RoleTypes[1] = _roleService.GetAvailableRoleTypes(UserRole.DirectManager);
            dto.RoleTypes[2] = _roleService.GetAvailableRoleTypes(UserRole.AssistantManager);
            dto.RoleTypes[3] = _roleService.GetAvailableRoleTypes(UserRole.Coordinator);
            dto.RoleTypes[4] = _roleService.GetAvailableRoleTypes(UserRole.Supervisor);
            dto.RoleTypes[5] = _roleService.GetAvailableRoleTypes(UserRole.Manager);
            return ServiceResult<AccountsDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching users with term {SearchTerm}", searchTerm);
            return ServiceResult<AccountsDto>.Failure("حدث خطأ أثناء البحث");
        }
    }

    public async Task<ServiceResult> ChangeUserPasswordAsync(AdminChangePasswordDto dto)
    {
        // Use validation service for password confirmation
        var confirmationValidation = _validationService.ValidatePasswordConfirmation(dto.NewPassword, dto.ConfirmPassword);
        if (!confirmationValidation.IsSuccess)
            return confirmationValidation;

        // Let UserService handle password validation and setting
        var result = await _userService.SetUserPasswordAsync(dto.Username, dto.NewPassword);
        if (result.IsSuccess)
            return ServiceResult.Success("تم تغيير كلمة المرور بنجاح");
        return ServiceResult.Failure(result.Message);
    }

    public bool IsValidEmail(string email)
    {
        // Delegate to validation service
        var result = _validationService.ValidateEmail(email);
        return result.IsSuccess;
    }


    private async Task LoadAccountsData(AccountsDto dto)
    {
        var usersResult = await _userService.GetAllUsersAsync();
        if (usersResult.IsSuccess)
        {
            dto.Users = usersResult.Data?.Where(u => u.Username != "Super Admin").ToList() ?? [];
        }
    }
}
