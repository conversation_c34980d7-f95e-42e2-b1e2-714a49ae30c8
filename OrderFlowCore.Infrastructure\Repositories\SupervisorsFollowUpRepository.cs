using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Infrastructure.Repositories
{
    public class SupervisorsFollowUpRepository : ISupervisorsFollowUpRepository
    {
        private readonly ApplicationDbContext _context;
        public SupervisorsFollowUpRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<SupervisorsFollowUp>> GetBySupervisorAsync(string supervisorId)
        {
            return await _context.SupervisorsFollowUps
                .Where(x => x.SupervisorId == supervisorId)
                .OrderBy(x => x.OwnerName)
                .ToListAsync();
        }

        public async Task<SupervisorsFollowUp> GetAsync(string supervisorId, string civilRecord)
        {
            return await _context.SupervisorsFollowUps
                .FirstOrDefaultAsync(x => x.SupervisorId == supervisorId && x.CivilRecord == civilRecord);
        }

        public async Task AddAsync(SupervisorsFollowUp entity)
        {
            _context.SupervisorsFollowUps.Add(entity);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(SupervisorsFollowUp entity)
        {
            _context.SupervisorsFollowUps.Update(entity);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(string supervisorId, string civilRecord)
        {
            var entity = await GetAsync(supervisorId, civilRecord);
            if (entity != null)
            {
                _context.SupervisorsFollowUps.Remove(entity);
                await _context.SaveChangesAsync();
            }
        }

        public async Task DeleteAllAsync(string supervisorId)
        {
            var records = await _context.SupervisorsFollowUps
                .Where(x => x.SupervisorId == supervisorId)
                .ToListAsync();
            _context.SupervisorsFollowUps.RemoveRange(records);
            await _context.SaveChangesAsync();
        }

        public async Task<bool> AnyAsync(string supervisorId, string civilRecord)
        {
            return await _context.SupervisorsFollowUps.AnyAsync(x => x.SupervisorId == supervisorId && x.CivilRecord == civilRecord);
        }

        public async Task<List<SupervisorsFollowUp>> GetAllAsync()
        {
            return await _context.SupervisorsFollowUps.ToListAsync();
        }
    }
} 