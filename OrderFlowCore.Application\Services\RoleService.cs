using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using System.Collections.Generic;
using System.Linq;

namespace OrderFlowCore.Application.Services
{
    public class RoleService : IRoleService
    {
        public List<string> GetAvailableRoleTypes(UserRole userRole)
        {
            return userRole switch
            {
                UserRole.Admin => new List<string>(), // Admin has no role types
                UserRole.DirectManager => GetDepartmentTypes(),
                UserRole.AssistantManager => GetAssistantManagerTypes(),
                UserRole.Coordinator => new List<string>(), // Coordinator has no role types
                UserRole.Supervisor => GetSupervisorTypes(),
                UserRole.Manager => new List<string>(), // Manager has no role types
                _ => new List<string>()
            };
        }

        public List<string> GetDepartmentTypes()
        {
            // These would typically come from the database
            // For now, returning some common department types
            return new List<string>
            {
                "قسم إدارة الأصول",
                "قسم المشتريات",
                "قسم الصيانة",
                "قسم المخازن",
                "قسم الموارد البشرية",
                "قسم المالية",
                "قسم تقنية المعلومات",
                "قسم الخدمات الطبية",
                "قسم التمريض",
                "قسم الخدمات الإدارية"
            };
        }

        public List<string> GetAssistantManagerTypes()
        {
            return new List<string>
            {
                "مساعد المدير للخدمات الطبية",
                "مساعد المدير لخدمات التمريض",
                "مساعد المدير للخدمات الإدارية والتشغيل",
                "مساعد المدير للموارد البشرية"
            };
        }

        public List<string> GetSupervisorTypes()
        {
            return new List<string>
            {
                "خدمات الموظفين",
                "إدارة تخطيط الموارد البشرية",
                "إدارة تقنية المعلومات",
                "مراقبة الدوام",
                "السجلات الطبية",
                "إدارة الرواتب والاستحقاقات",
                "إدارة القانونية والالتزام",
                "خدمات الموارد البشرية",
                "إدارة الإسكان",
                "قسم الملفات",
                "العيادات الخارجية",
                "التأمينات الاجتماعية",
                "وحدة مراقبة المخزون",
                "إدارة تنمية الإيرادات",
                "إدارة الأمن و السلامة",
                "الطب الاتصالي"
            };
        }

        public string GetRoleDisplayName(UserRole userRole, string? roleType)
        {
            var baseRole = userRole.ToDisplayName();
            
            if (!string.IsNullOrEmpty(roleType))
            {
                return $"{baseRole} - {roleType}";
            }
            
            return baseRole;
        }

        public bool ShouldShowRoleType(UserRole userRole)
        {
            return userRole switch
            {
                UserRole.DirectManager => true,
                UserRole.AssistantManager => true,
                UserRole.Supervisor => true,
                UserRole.Admin => false,
                UserRole.Coordinator => false,
                UserRole.Manager => false,
                _ => false
            };
        }

        public bool ValidateRoleType(UserRole userRole, string? roleType)
        {
            if (string.IsNullOrEmpty(roleType))
            {
                // Some roles might not require a specific type
                return !ShouldShowRoleType(userRole);
            }

            var availableTypes = GetAvailableRoleTypes(userRole);
            return availableTypes.Contains(roleType);
        }

        public SupervisorType? GetSupervisorTypeFromString(string supervisorTypeString)
        {
            return supervisorTypeString switch
            {
                "خدمات الموظفين" => SupervisorType.EmployeeServices,
                "إدارة تخطيط الموارد البشرية" => SupervisorType.HumanResourcesPlanning,
                "إدارة تقنية المعلومات" => SupervisorType.InformationTechnology,
                "مراقبة الدوام" => SupervisorType.AttendanceMonitoring,
                "السجلات الطبية" => SupervisorType.MedicalRecords,
                "إدارة الرواتب والاستحقاقات" => SupervisorType.PayrollAndBenefits,
                "إدارة القانونية والالتزام" => SupervisorType.LegalAndCompliance,
                "خدمات الموارد البشرية" => SupervisorType.HumanResourcesServices,
                "إدارة الإسكان" => SupervisorType.HousingManagement,
                "قسم الملفات" => SupervisorType.FilesSection,
                "العيادات الخارجية" => SupervisorType.OutpatientClinics,
                "التأمينات الاجتماعية" => SupervisorType.SocialInsurance,
                "وحدة مراقبة المخزون" => SupervisorType.InventoryMonitoring,
                "إدارة تنمية الإيرادات" => SupervisorType.RevenueManagement,
                "إدارة الأمن و السلامة" => SupervisorType.SecurityAndSafety,
                "الطب الاتصالي" => SupervisorType.Telemedicine,
                _ => null
            };
        }

        public AssistantManagerType? GetAssistantManagerTypeFromString(string assistantManagerTypeString)
        {
            return assistantManagerTypeString switch
            {
                "مساعد المدير للخدمات الطبية" => AssistantManagerType.A1,
                "مساعد المدير لخدمات التمريض" => AssistantManagerType.A2,
                "مساعد المدير للخدمات الإدارية والتشغيل" => AssistantManagerType.A3,
                "مساعد المدير للموارد البشرية" => AssistantManagerType.A4,
                _ => null
            };
        }
    }
}
