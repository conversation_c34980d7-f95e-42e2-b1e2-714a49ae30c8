using System.Collections.Generic;

namespace OrderFlowCore.Application.DTOs
{
    public class AccountsDto
    {
        public List<UserDto> Users { get; set; } = new();
        public string UserSearchTerm { get; set; } = string.Empty;
        public UserCreateDto AddModel { get; set; } = new UserCreateDto();
        public UserEditDto EditModel { get; set; } = new UserEditDto();
        public AdminChangePasswordDto ChangePasswordModel { get; set; }
    }
} 