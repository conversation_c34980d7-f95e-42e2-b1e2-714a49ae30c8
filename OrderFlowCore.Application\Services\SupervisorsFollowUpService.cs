using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Core.Models;
using System.IO;
using System.Text;

namespace OrderFlowCore.Application.Services
{
    public class SupervisorsFollowUpService : ISupervisorsFollowUpService
    {
        private readonly IUnitOfWork _unitOfWork;
        public SupervisorsFollowUpService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<List<SupervisorsFollowUpDto>> GetBySupervisorAsync(string supervisorId)
        {
            var records = await _unitOfWork.SupervisorsFollowUps.GetBySupervisorAsync(supervisorId);
            return records.OrderBy(x => x.OwnerName).Select(MapToDto).ToList();
        }

        public async Task<SupervisorsFollowUpDto> GetAsync(string supervisorId, string civilRecord)
        {
            var entity = await _unitOfWork.SupervisorsFollowUps.GetAsync(supervisorId, civilRecord);
            return entity == null ? null : MapToDto(entity);
        }

        public async Task AddAsync(SupervisorsFollowUpDto record)
        {
            var entity = MapToEntity(record);
            await _unitOfWork.SupervisorsFollowUps.AddAsync(entity);
            await _unitOfWork.SaveChangesAsync();
        }

        public async Task UpdateAsync(SupervisorsFollowUpDto record)
        {
            var entity = await _unitOfWork.SupervisorsFollowUps.GetAsync(record.SupervisorId, record.CivilRecord);

            if (entity != null)
            {
                entity.OwnerName = record.OwnerName;
                entity.SpecialProcedure = record.SpecialProcedure;
                await _unitOfWork.SupervisorsFollowUps.UpdateAsync(entity);
                await _unitOfWork.SaveChangesAsync();
            }
        }

        public async Task DeleteAsync(string supervisorId, string civilRecord)
        {

            await _unitOfWork.SupervisorsFollowUps.DeleteAsync(supervisorId, civilRecord);
            await _unitOfWork.SaveChangesAsync();

        }

        public async Task DeleteAllAsync(string supervisorId)
        {
            await _unitOfWork.SupervisorsFollowUps.DeleteAllAsync(supervisorId);
            await _unitOfWork.SaveChangesAsync();
        }

        public async Task<int> ImportAsync(string supervisorId, Stream csvStream)
        {
            int count = 0;
            using (var reader = new StreamReader(csvStream, Encoding.UTF8))
            {
                bool isFirstLine = true;
                while (!reader.EndOfStream)
                {
                    var line = await reader.ReadLineAsync();
                    if (isFirstLine)
                    {
                        isFirstLine = false;
                        continue; // skip header
                    }
                    if (string.IsNullOrWhiteSpace(line)) continue;
                    var fields = line.Split(';');
                    if (fields.Length < 4) continue;
                    var civilRecord = fields[1].Trim();
                    var ownerName = fields[2].Trim();
                    var specialProcedure = fields[3].Trim();
                    // Check for duplicate
                    var exists = await _unitOfWork.SupervisorsFollowUps.AnyAsync( supervisorId, civilRecord);
                    if (exists) continue;
                    var entity = new SupervisorsFollowUp
                    {
                        SupervisorId = supervisorId,
                        CivilRecord = civilRecord,
                        OwnerName = ownerName,
                        SpecialProcedure = specialProcedure
                    };
                    await _unitOfWork.SupervisorsFollowUps.AddAsync(entity);
                    count++;
                }
                await _unitOfWork.SaveChangesAsync();
            }
            return count;
        }

        public async Task<byte[]> ExportAsync(string supervisorId)
        {
            var records = await _unitOfWork.SupervisorsFollowUps.GetBySupervisorAsync(supervisorId);
            var sb = new StringBuilder();
            sb.AppendLine("SupervisorId;CivilRecord;OwnerName;SpecialProcedure");
            foreach (var r in records)
            {
                sb.AppendLine($"{r.SupervisorId};{r.CivilRecord};{r.OwnerName};{r.SpecialProcedure}");
            }
            return Encoding.UTF8.GetBytes(sb.ToString());
        }

        private SupervisorsFollowUpDto MapToDto(SupervisorsFollowUp entity)
        {
            return new SupervisorsFollowUpDto
            {
                SupervisorId = entity.SupervisorId,
                CivilRecord = entity.CivilRecord,
                OwnerName = entity.OwnerName,
                SpecialProcedure = entity.SpecialProcedure
            };
        }

        private SupervisorsFollowUp MapToEntity(SupervisorsFollowUpDto dto)
        {
            return new SupervisorsFollowUp
            {
                SupervisorId = dto.SupervisorId,
                CivilRecord = dto.CivilRecord,
                OwnerName = dto.OwnerName,
                SpecialProcedure = dto.SpecialProcedure
            };
        }
    }
}